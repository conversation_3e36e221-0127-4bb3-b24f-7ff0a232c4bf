/**
 * @jest-environment jsdom
 */

import { render, screen } from '@testing-library/react';
import * as React from 'react';

// Mock all external dependencies that would cause issues in the test environment
jest.mock('@/i18n/navigation', () => ({
  Link: ({ children, ...props }: any) => React.createElement('a', props, children),
  useRouter: () => ({ push: jest.fn() }),
}));

jest.mock('@/components/core/logo', () => ({
  DynamicLogo: () => React.createElement('div', { 'data-testid': 'logo' }, 'Logo'),
}));

jest.mock('@/contexts/firebase-auth-context', () => ({
  useAuth: () => ({ user: null, loading: false }),
}));

jest.mock('@/paths', () => ({
  paths: {
    home: '/app/home',
    auth: { signIn: '/auth/sign-in', signUp: '/auth/sign-up' },
  },
}));

// Mock next-intl with simple string returns
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => {
    // Return mock translations based on key
    const mockTranslations: Record<string, string> = {
      'header.termsOfService': 'Terms of service',
      'header.privacyPolicy': 'Privacy policy',
      'header.bookDemo': 'Book a demo',
      'header.features': 'Features',
      'header.pricing': 'Pricing',
      'header.integrations': 'Integrations',
      'badge.trustedBy': 'Trusted by engineering leaders at',
      'hero.title': 'Strategic Engineering Intelligence for',
      'hero.titleHighlight': 'Executive Decision Making',
      'hero.subtitle':
        'Multi-agent AI system delivering personalized insights and predictive analytics tailored for CTOs and engineering leaders.',
      'hero.bookDemo': 'Schedule Executive Demo',
      'hero.signIn': 'Sign In',
      'hero.enterApp': 'Enter Dashboard',
      'hero.ctaSecondary': 'View Live Demo',
      'features.title': 'AI-Powered Engineering Intelligence',
      'features.subtitle': 'Purpose-built for executive decision making and strategic planning',
      'features.aiAgents.title': 'Multi-Agent AI System',
      'features.aiAgents.description':
        'Specialized AI agents for different personas - CTOs get strategic insights, Engineering Managers get operational metrics',
      'features.integrations.title': 'Enterprise Integration Ecosystem',
      'features.integrations.description':
        'Seamless integration with GitHub, Jira, Azure DevOps, Slack, and 20+ development tools',
      'features.insights.title': 'Predictive Analytics & Forecasting',
      'features.insights.description':
        'Advanced algorithms predict delivery timelines, identify bottlenecks, and recommend resource allocation',
      'features.security.title': 'Enterprise-Grade Security',
      'features.security.description': 'SOC 2 compliant with end-to-end encryption and role-based access controls',
      'socialProof.title': 'Trusted by Engineering Leaders',
      'socialProof.subtitle':
        'Join hundreds of CTOs and engineering executives who rely on BSM Tech Pulse for strategic decision making',
      'socialProof.testimonials.cto.quote':
        'BSM Tech Pulse transformed how we make strategic engineering decisions. The AI insights helped us identify bottlenecks we never knew existed and improved our delivery velocity by 45%.',
      'socialProof.testimonials.cto.name': 'Sarah Chen',
      'socialProof.testimonials.cto.title': 'CTO, TechCorp • 200+ Engineers',
      'socialProof.testimonials.vp.quote':
        'The multi-agent AI system is game-changing. It gives me executive-level insights while providing my engineering managers with the operational metrics they need. ROI was evident within the first month.',
      'socialProof.testimonials.vp.name': 'Michael Rodriguez',
      'socialProof.testimonials.vp.title': 'VP Engineering, InnovateLabs • 150+ Engineers',
      'pricing.title': 'Executive Pricing Plans',
      'pricing.subtitle': 'Scalable solutions designed for growing engineering organizations',
      'pricing.manager.title': 'Team Manager',
      'pricing.manager.price': '$999',
      'pricing.manager.period': 'per month',
      'pricing.manager.description': 'For Engineering Managers',
      'pricing.cto.title': 'Executive Suite',
      'pricing.cto.price': '$2,999',
      'pricing.cto.period': 'per month',
      'pricing.cto.description': 'For CTOs and VP Engineering',
      'pricing.enterprise.title': 'Enterprise',
      'pricing.enterprise.price': 'Custom',
      'pricing.enterprise.period': 'pricing',
      'pricing.enterprise.description': 'For large organizations',
      'mockup.projectTitle': 'Reorder projects to accelerate delivery by 50%',
      'mockup.projectSubtitle': 'AI recommends shifting top priorities to your highest-velocity teams',
      'mockup.analyzingStack': 'Analyzing development stack',
      'mockup.aiInsight': 'AI Insight: Team Alpha shows 40% higher velocity on React projects',
      'footer.copyright': '© 2025 BSM Tech Pulse. All rights reserved.',
      'footer.tagline': 'Strategic Engineering Intelligence Platform',
    };
    return mockTranslations[key] || key;
  },
}));

describe('LandingPage', () => {
  it('should render without crashing', async () => {
    const { LandingPage } = await import('../landing-page');

    expect(() => {
      render(<LandingPage />);
    }).not.toThrow();
  });
  it('should display hero content', async () => {
    const { LandingPage } = await import('../landing-page');

    render(<LandingPage />);

    // Check that the main heading is present
    expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument();
    expect(screen.getByText(/Strategic Engineering Intelligence for/)).toBeInTheDocument();
    expect(screen.getByText(/Executive Decision Making/)).toBeInTheDocument();
    expect(screen.getByText(/Multi-agent AI system delivering personalized insights/)).toBeInTheDocument();
  });

  it('should display product mockup section', async () => {
    const { LandingPage } = await import('../landing-page');

    render(<LandingPage />);

    expect(screen.getByText('Reorder projects to accelerate delivery by 50%')).toBeInTheDocument();
    expect(
      screen.getByText('AI recommends shifting top priorities to your highest-velocity teams')
    ).toBeInTheDocument();
    expect(screen.getByText('Analyzing development stack')).toBeInTheDocument();
  });

  it('should display navigation buttons', async () => {
    const { LandingPage } = await import('../landing-page');

    render(<LandingPage />);

    // Check for header navigation
    expect(screen.getByText('Features')).toBeInTheDocument();
    expect(screen.getByText('Pricing')).toBeInTheDocument();
    expect(screen.getByText('Integrations')).toBeInTheDocument();
    expect(screen.getByText('Schedule Executive Demo')).toBeInTheDocument();
  });

  it('should display features section', async () => {
    const { LandingPage } = await import('../landing-page');

    render(<LandingPage />);

    expect(screen.getByText('AI-Powered Engineering Intelligence')).toBeInTheDocument();
    expect(screen.getByText('Multi-Agent AI System')).toBeInTheDocument();
    expect(screen.getByText('Enterprise Integration Ecosystem')).toBeInTheDocument();
    expect(screen.getByText('Predictive Analytics & Forecasting')).toBeInTheDocument();
    expect(screen.getByText('Enterprise-Grade Security')).toBeInTheDocument();
  });

  it('should display social proof section', async () => {
    const { LandingPage } = await import('../landing-page');

    render(<LandingPage />);

    expect(screen.getByText('Trusted by Engineering Leaders')).toBeInTheDocument();
    expect(screen.getByText('Sarah Chen')).toBeInTheDocument();
    expect(screen.getByText('Michael Rodriguez')).toBeInTheDocument();
  });

  it('should display pricing section', async () => {
    const { LandingPage } = await import('../landing-page');

    render(<LandingPage />);

    expect(screen.getByText('Executive Pricing Plans')).toBeInTheDocument();
    expect(screen.getByText('Team Manager')).toBeInTheDocument();
    expect(screen.getByText('Executive Suite')).toBeInTheDocument();
    expect(screen.getByText('Enterprise')).toBeInTheDocument();
    expect(screen.getByText('$999')).toBeInTheDocument();
    expect(screen.getByText('$2,999')).toBeInTheDocument();
  });

  it('should display integration showcase section', async () => {
    const { LandingPage } = await import('../landing-page');

    render(<LandingPage />);

    expect(screen.getByText('Seamless Integration Ecosystem')).toBeInTheDocument();
    expect(screen.getByText('Development & Version Control')).toBeInTheDocument();
    expect(screen.getByText('Project Management & Planning')).toBeInTheDocument();
    expect(screen.getByText('Communication & Collaboration')).toBeInTheDocument();

    // Check for specific integrations using getAllByText for duplicates
    expect(screen.getAllByText('GitHub')).toHaveLength(2); // One in mockup, one in integrations
    expect(screen.getByText('GitLab')).toBeInTheDocument();
    expect(screen.getByText('Bitbucket')).toBeInTheDocument();
    expect(screen.getAllByText('Jira')).toHaveLength(2); // One in mockup, one in integrations
    expect(screen.getByText('Azure DevOps')).toBeInTheDocument();
    expect(screen.getByText('Linear')).toBeInTheDocument();
    expect(screen.getByText('Slack')).toBeInTheDocument();
    expect(screen.getByText('Microsoft Teams')).toBeInTheDocument();
    expect(screen.getByText('Discord')).toBeInTheDocument();

    // Check for integration benefits
    expect(screen.getByText('Enterprise-Grade Integration Benefits')).toBeInTheDocument();
    expect(screen.getByText('One-Click Setup')).toBeInTheDocument();
    expect(screen.getByText('Enterprise Security')).toBeInTheDocument();
    expect(screen.getByText('Real-Time Sync')).toBeInTheDocument();
  });
});
